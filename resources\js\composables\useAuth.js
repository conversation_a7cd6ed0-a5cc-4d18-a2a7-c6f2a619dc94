import { ref, computed } from 'vue'
import { useRouter } from 'vue-router'
import { useApi } from './useApi'

// Global state for authentication
const user = ref(null)
const isAuthenticated = ref(false)
const hasAdminAccess = ref(false)
const authLoading = ref(false)
const authError = ref(null)

export function useAuth() {
  const router = useRouter()
  const { get } = useApi()

  // Computed properties
  const isAdmin = computed(() => user.value?.role === 'Admin')
  const canAccessWizard = computed(() => isAuthenticated.value && hasAdminAccess.value)

  // Extract URL parameters
  const getUrlParams = () => {
    const urlParams = new URLSearchParams(window.location.search)
    return {
      portal_id: urlParams.get('portal_id'),
      user_id: urlParams.get('user_id'),
      email: urlParams.get('email')
    }
  }

  // Check user authentication and role
  const checkUserAccess = async () => {
    authLoading.value = true
    authError.value = null

    try {
      const params = getUrlParams()
      
      if (!params.portal_id || !params.user_id) {
        throw new Error('Missing required authentication parameters')
      }

      console.log('Checking user access with params:', { 
        portal_id: params.portal_id, 
        user_id: params.user_id ? 'present' : 'missing',
        email: params.email 
      })

      const response = await get('https://api.niswey.net/api/user', {
        portal_id: params.portal_id,
        user_id: params.user_id
      })
      console.log('API Response:', response);

      if (response.ok) {
        user.value = response.data
        isAuthenticated.value = true
        hasAdminAccess.value = response.hasAccess
        
        console.log('User authentication result:', {
          email: user.value.email,
          role: user.value.role,
          hasAccess: hasAdminAccess.value
        })

        return {
          success: true,
          user: user.value,
          hasAccess: hasAdminAccess.value
        }
      } else {
        throw new Error(response.error || 'Authentication failed')
      }
    } catch (error) {
      console.error('Authentication error:', error)
      authError.value = error.message
      user.value = null
      isAuthenticated.value = false
      hasAdminAccess.value = false
      
      return {
        success: false,
        error: error.message
      }
    } finally {
      authLoading.value = false
    }
  }

  // Redirect to access denied page
  const redirectToAccessDenied = () => {
    router.push('/error?message=Access denied. Admin role required to access the wizard.')
  }

  // Redirect to auth page
  const redirectToAuth = () => {
    router.push('/auth')
  }

  // Logout function
  const logout = () => {
    user.value = null
    isAuthenticated.value = false
    hasAdminAccess.value = false
    authError.value = null
    redirectToAuth()
  }

  // Clear auth state
  const clearAuth = () => {
    user.value = null
    isAuthenticated.value = false
    hasAdminAccess.value = false
    authError.value = null
  }

  return {
    // State
    user,
    isAuthenticated,
    hasAdminAccess,
    authLoading,
    authError,
    
    // Computed
    isAdmin,
    canAccessWizard,
    
    // Methods
    checkUserAccess,
    redirectToAccessDenied,
    redirectToAuth,
    logout,
    clearAuth,
    getUrlParams
  }
}
